{"name": "mov", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@react-email/components": "^0.0.21", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.51.9", "axios": "^1.7.2", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "embla-carousel-react": "^8.1.8", "framer-motion": "^12.18.1", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.408.0", "mongoose": "^8.5.1", "next": "14.2.5", "next-themes": "^0.3.0", "nodemailer": "^6.9.14", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.52.1", "react-hot-toast": "^2.4.1", "react-simple-typewriter": "^5.0.1", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20", "@types/nodemailer": "^6.4.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}