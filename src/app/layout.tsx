import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Inter as FontSans } from "next/font/google";
import "@/styles/globals.css";

import { QueryClientProviderWrapper } from "@/helper/querryclient-provider";
import { Toaster } from "react-hot-toast";
const inter = Inter({ subsets: ["latin"] });
import { AuthContextProvider } from "@/context/Auth";
import { cn } from "@/lib/utils";
import { ThemeProvider } from "@/helper/theme-provider";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Film - Mix",
  description: "",
  icons: {
    icon: "/favicon.svg",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased ",
          fontSans.variable
        )}
      >
        <QueryClientProviderWrapper>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <AuthContextProvider>
              {children}
              <Toaster />
            </AuthContextProvider>
          </ThemeProvider>
        </QueryClientProviderWrapper>
      </body>
    </html>
  );
}
